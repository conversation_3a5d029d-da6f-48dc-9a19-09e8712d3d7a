#!/usr/bin/env python3
"""
Test script to validate merchant address variations against reference implementation.
Creates 10+ test cases with different merchant address combinations and compares
our implementation against the reference implementation.
"""

import json
import subprocess
import os
import sys
from datetime import datetime

def create_test_case(name, merchant_name, address_line2, postcode="SW18 4GG", 
                    card_number="****************", amount=1000, currency="GBP", cvc="123"):
    """Create a test case JSON file with specified parameters"""
    test_data = {
        "timestamp": "2025-05-31T11:55:39.000Z",
        "amount": {
            "value": amount,
            "currency": currency
        },
        "card": {
            "number": card_number,
            "expiry_month": 12,
            "expiry_year": 2025,
            "cvc": cvc
        },
        "merchant": {
            "name": merchant_name,
            "address": {
                "line1": "Merchant Street",
                "line2": address_line2,
                "postcode": postcode
            }
        }
    }
    
    filename = f"test_address_var_{name}.json"
    with open(filename, 'w') as f:
        json.dump(test_data, f, indent=2)
    return filename

def run_converter(jar_file, input_file, output_file):
    """Run a converter JAR and return the output"""
    try:
        result = subprocess.run([
            'java', '-jar', jar_file, input_file, output_file
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print(f"  ❌ Error running {jar_file}: {result.stderr}")
            return None
            
        # Read the output file
        if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                return f.read().strip()
        else:
            print(f"  ❌ Output file {output_file} not created")
            return None
            
    except subprocess.TimeoutExpired:
        print(f"  ❌ Timeout running {jar_file}")
        return None
    except Exception as e:
        print(f"  ❌ Exception running {jar_file}: {e}")
        return None

def test_address_variation(name, merchant_name, address_line2, postcode="SW18 4GG"):
    """Test a specific address variation against both implementations"""
    print(f"Testing {name}: merchant='{merchant_name}', line2='{address_line2}', postcode='{postcode}'")
    
    # Create test file
    test_file = create_test_case(name, merchant_name, address_line2, postcode)
    
    # Test reference implementation
    ref_output_file = f'ref_address_var_{name}.txt'
    ref_output = run_converter('referenceConverterCli.jar', test_file, ref_output_file)
    if ref_output is None:
        print(f"  ❌ Reference failed for {name}")
        return False
    
    # Test our implementation
    our_output_file = f'our_address_var_{name}.txt'
    our_output = run_converter('build/libs/card-payment-converter-1.0.0-all.jar', test_file, our_output_file)
    if our_output is None:
        print(f"  ❌ Our implementation failed for {name}")
        return False
    
    # Compare outputs
    if ref_output == our_output:
        print(f"  ✅ MATCH for {name}")
        return True
    else:
        print(f"  ❌ MISMATCH for {name}")
        print(f"     Reference: {ref_output}")
        print(f"     Our impl:  {our_output}")
        print(f"     Diff length: ref={len(ref_output)}, ours={len(our_output)}")
        return False

def main():
    """Run all address variation tests"""
    print("=== Merchant Address Variation Testing ===")
    print("Testing our implementation against reference implementation")
    print()
    
    # Test cases covering different address variations
    test_cases = [
        # Basic variations
        ("short_addr", "Store", "NY"),
        ("medium_addr", "Store", "London"),
        ("long_addr", "Store", "Manchester"),
        ("very_long_addr", "Store", "VeryLongAddressLine2Name"),
        
        # Different merchant names with same address
        ("short_merchant", "A", "London"),
        ("medium_merchant", "MediumName", "London"),
        ("long_merchant", "VeryLongMerchantName", "London"),
        ("max_merchant", "ExactlyElev", "London"),  # 11 chars (max)
        ("over_max_merchant", "TwelveCharac", "London"),  # 12 chars (truncated)
        
        # Special known cases
        ("store_ny", "Store", "NY"),  # Known special case
        ("store_manchester", "Store", "Manchester"),  # Known special case
        
        # Different postcodes
        ("uk_postcode", "Store", "London", "M1 1AA"),
        ("us_postcode", "Store", "NewYork", "10001"),
        ("long_postcode", "Store", "London", "SW18 4GG ABC"),
        
        # Edge cases
        ("empty_merchant", "", "London"),
        ("single_char_addr", "Store", "X"),
        ("numbers_in_addr", "Store", "123Main"),
        ("special_chars", "Store", "Addr-Line_2"),
    ]
    
    passed = 0
    failed = 0
    
    for name, merchant, addr2, *postcode in test_cases:
        pc = postcode[0] if postcode else "SW18 4GG"
        if test_address_variation(name, merchant, addr2, pc):
            passed += 1
        else:
            failed += 1
        print()
    
    print("=== SUMMARY ===")
    print(f"Total tests: {passed + failed}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed > 0:
        print(f"\n❌ {failed} test(s) failed. Implementation needs fixes.")
        return 1
    else:
        print(f"\n✅ All tests passed! Implementation is correct.")
        return 0

if __name__ == "__main__":
    sys.exit(main())
