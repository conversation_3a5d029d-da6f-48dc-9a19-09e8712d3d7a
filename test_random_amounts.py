#!/usr/bin/env python3
import json
import random
import subprocess
import sys

def create_test_file(amount, filename):
    """Create a test JSON file with the given amount"""
    test_data = {
        "timestamp": "2025-05-31T11:55:39.000Z",
        "amount": {
            "value": amount,
            "currency": "GBP"
        },
        "card": {
            "number": "****************",
            "expiry_month": 12,
            "expiry_year": 2025,
            "cvc": "123"
        },
        "merchant": {
            "name": "Merchant Name",
            "address": {
                "line1": "Merchant Street",
                "line2": "London",
                "postcode": "SW18 4GG"
            }
        }
    }
    
    with open(filename, 'w') as f:
        json.dump(test_data, f, indent=2)

def run_converter(jar_file, input_file, output_file):
    """Run a converter and return the output"""
    try:
        result = subprocess.run([
            'java', '-jar', jar_file, input_file, output_file
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            # Extract the hex output from stdout
            for line in result.stdout.split('\n'):
                if line.startswith('Output: '):
                    return line.replace('Output: ', '').strip()
        else:
            print(f"Error running {jar_file}: {result.stderr}")
            return None
    except subprocess.TimeoutExpired:
        print(f"Timeout running {jar_file}")
        return None
    except Exception as e:
        print(f"Exception running {jar_file}: {e}")
        return None

def test_amount(amount):
    """Test a specific amount against both implementations"""
    print(f"Testing amount: {amount:,}")
    
    # Create test file
    test_file = f"test_random_{amount}.json"
    create_test_file(amount, test_file)
    
    # Test reference implementation
    ref_output = run_converter('referenceConverterCli.jar', test_file, f'ref_random_{amount}.txt')
    if ref_output is None:
        print(f"  ❌ Reference failed for amount {amount}")
        return False
    
    # Test our implementation
    our_output = run_converter('build/libs/card-payment-converter-1.0.0-all.jar', test_file, f'our_random_{amount}.txt')
    if our_output is None:
        print(f"  ❌ Our implementation failed for amount {amount}")
        return False
    
    # Compare outputs
    if ref_output == our_output:
        print(f"  ✅ MATCH")
        return True
    else:
        print(f"  ❌ MISMATCH")
        print(f"    Reference: {ref_output}")
        print(f"    Our impl:  {our_output}")
        return False

def main():
    """Test random amounts across different ranges"""
    print("🧪 Testing random payment amounts across full range...")
    print("=" * 60)
    
    # Test ranges
    test_ranges = [
        (0, 10),                    # Very small amounts
        (10, 1000),                 # Small amounts  
        (1000, 100000),             # Medium amounts
        (100000, 1000000),          # Large amounts
        (1000000, 100000000),       # Very large amounts
        (100000000, 2147483647),    # Maximum int range
    ]
    
    total_tests = 0
    passed_tests = 0
    
    # Test specific boundary values
    boundary_values = [0, 1, 999, 1000, 9999, 10000, 99999, 100000, 999999, 1000000, 2147483647]
    
    print("Testing boundary values:")
    for amount in boundary_values:
        total_tests += 1
        if test_amount(amount):
            passed_tests += 1
    
    print("\nTesting random values:")
    # Test random values from each range
    for min_val, max_val in test_ranges:
        print(f"\nRange {min_val:,} - {max_val:,}:")
        for _ in range(3):  # 3 random tests per range
            amount = random.randint(min_val, max_val)
            total_tests += 1
            if test_amount(amount):
                passed_tests += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Payment amount conversion is 100% accurate.")
    else:
        print(f"⚠️  {total_tests - passed_tests} tests failed. Need investigation.")

if __name__ == "__main__":
    main()
