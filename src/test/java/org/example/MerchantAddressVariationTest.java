package org.example;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import org.example.model.PaymentRequest;
import java.time.Instant;

/**
 * Comprehensive test suite for merchant address variations.
 * These tests validate that our implementation correctly handles different
 * combinations of merchant names, address line2 values, and postcodes
 * according to the reverse-engineered binary format rules.
 */
public class MerchantAddressVariationTest {

    private BinaryConverter converter;

    @BeforeEach
    void setUp() {
        converter = new BinaryConverter();
    }

    /**
     * Helper method to create a payment request with specified merchant details
     */
    private PaymentRequest createPaymentRequest(String merchantName, String addressLine2, String postcode) {
        PaymentRequest request = new PaymentRequest();
        request.setTimestamp(Instant.parse("2025-05-31T11:55:39.000Z"));

        PaymentRequest.Amount amount = new PaymentRequest.Amount();
        amount.setValue(1000);
        amount.setCurrency("GBP");
        request.setAmount(amount);

        PaymentRequest.Card card = new PaymentRequest.Card();
        card.setNumber("****************");
        card.setExpiryMonth(12);
        card.setExpiryYear(2025);
        card.setCvc("123");
        request.setCard(card);

        PaymentRequest.Merchant merchant = new PaymentRequest.Merchant();
        merchant.setName(merchantName);

        PaymentRequest.Address address = new PaymentRequest.Address();
        address.setLine1("Merchant Street");
        address.setLine2(addressLine2);
        address.setPostcode(postcode);
        merchant.setAddress(address);

        request.setMerchant(merchant);
        return request;
    }

    @Test
    @DisplayName("Store merchant with special address cases")
    void testStoreSpecialCases() {
        // Special case: Store + NY
        PaymentRequest request1 = createPaymentRequest("Store", "NY", "SW18 4GG");
        String result1 = converter.convertToBinary(request1);
        assertTrue(result1.contains("19"), "Store + NY should use special contextual code 0x19");

        // Special case: Store + Manchester
        PaymentRequest request2 = createPaymentRequest("Store", "Manchester", "SW18 4GG");
        String result2 = converter.convertToBinary(request2);
        assertTrue(result2.contains("21"), "Store + Manchester should use special contextual code 0x21");
    }

    @Test
    @DisplayName("Store merchant with variable address line2 lengths")
    void testStoreAddressLengthVariations() {
        // Single character address
        PaymentRequest request1 = createPaymentRequest("Store", "X", "SW18 4GG");
        String result1 = converter.convertToBinary(request1);
        assertTrue(result1.contains("18"), "Store + X (1 char) should produce contextual code 0x18");

        // Medium length address (London = 6 chars, baseline)
        PaymentRequest request2 = createPaymentRequest("Store", "London", "SW18 4GG");
        String result2 = converter.convertToBinary(request2);
        assertTrue(result2.contains("1D"), "Store + London (6 chars) should produce contextual code 0x1D");

        // Long address
        PaymentRequest request3 = createPaymentRequest("Store", "VeryLongAddressLine2Name", "SW18 4GG");
        String result3 = converter.convertToBinary(request3);
        assertTrue(result3.contains("2F"), "Store + VeryLongAddressLine2Name (23 chars) should produce contextual code 0x2F");

        // Address with numbers
        PaymentRequest request4 = createPaymentRequest("Store", "123Main", "SW18 4GG");
        String result4 = converter.convertToBinary(request4);
        assertTrue(result4.contains("1E"), "Store + 123Main (7 chars) should produce contextual code 0x1E");

        // Address with special characters
        PaymentRequest request5 = createPaymentRequest("Store", "Addr-Line_2", "SW18 4GG");
        String result5 = converter.convertToBinary(request5);
        assertTrue(result5.contains("22"), "Store + Addr-Line_2 (11 chars) should produce contextual code 0x22");
    }

    @Test
    @DisplayName("Store merchant with variable postcode lengths")
    void testStorePostcodeLengthVariations() {
        // Shorter postcode (M1 1AA = 5 chars after space removal)
        PaymentRequest request1 = createPaymentRequest("Store", "London", "M1 1AA");
        String result1 = converter.convertToBinary(request1);
        assertTrue(result1.contains("1B"), "Store + London + M1 1AA should produce contextual code 0x1B");

        // US postcode (10001 = 5 chars)
        PaymentRequest request2 = createPaymentRequest("Store", "NewYork", "10001");
        String result2 = converter.convertToBinary(request2);
        assertTrue(result2.contains("1C"), "Store + NewYork + 10001 should produce contextual code 0x1C");

        // Longer postcode (SW18 4GG ABC = 10 chars after space removal)
        PaymentRequest request3 = createPaymentRequest("Store", "London", "SW18 4GG ABC");
        String result3 = converter.convertToBinary(request3);
        assertTrue(result3.contains("20"), "Store + London + SW18 4GG ABC should produce contextual code 0x20");
    }

    @Test
    @DisplayName("Non-Store merchants use simple formula")
    void testNonStoreMerchants() {
        // Single character merchant
        PaymentRequest request1 = createPaymentRequest("A", "London", "SW18 4GG");
        String result1 = converter.convertToBinary(request1);
        assertTrue(result1.contains("19"), "Merchant 'A' should produce contextual code 0x19 (0x18 + 1)");

        // Medium length merchant
        PaymentRequest request2 = createPaymentRequest("MediumName", "London", "SW18 4GG");
        String result2 = converter.convertToBinary(request2);
        assertTrue(result2.contains("22"), "Merchant 'MediumName' should produce contextual code 0x22 (0x18 + 10)");

        // Long merchant (truncated to 11 chars)
        PaymentRequest request3 = createPaymentRequest("VeryLongMerchantName", "London", "SW18 4GG");
        String result3 = converter.convertToBinary(request3);
        assertTrue(result3.contains("23"), "Long merchant should be truncated and produce contextual code 0x23 (0x18 + 11)");

        // Empty merchant
        PaymentRequest request4 = createPaymentRequest("", "London", "SW18 4GG");
        String result4 = converter.convertToBinary(request4);
        assertTrue(result4.contains("18"), "Empty merchant should produce contextual code 0x18 (0x18 + 0)");
    }

    @Test
    @DisplayName("Merchant name truncation works correctly")
    void testMerchantNameTruncation() {
        // Exactly 11 characters (max length)
        PaymentRequest request1 = createPaymentRequest("ExactlyElev", "London", "SW18 4GG");
        String result1 = converter.convertToBinary(request1);
        assertTrue(result1.contains("23"), "11-char merchant should produce contextual code 0x23 (0x18 + 11)");

        // 12 characters (should be truncated to 11)
        PaymentRequest request2 = createPaymentRequest("TwelveCharac", "London", "SW18 4GG");
        String result2 = converter.convertToBinary(request2);
        assertTrue(result2.contains("23"), "12-char merchant should be truncated and produce contextual code 0x23 (0x18 + 11)");
    }

    @Test
    @DisplayName("Contextual code formula validation")
    void testContextualCodeFormula() {
        // Test the discovered formula for Store merchants:
        // contextual_code = 0x18 + merchant_length + address_length - 6 + (postcode_length - 7)

        // Base case: Store (5) + London (6) + SW184GG (7) = 0x18 + 5 + 6 - 6 + 7 - 7 = 0x1D
        PaymentRequest baseRequest = createPaymentRequest("Store", "London", "SW18 4GG");
        String baseResult = converter.convertToBinary(baseRequest);
        assertTrue(baseResult.contains("1D"), "Base case should produce 0x1D");

        // Variation 1: Different address length
        // Store (5) + X (1) + SW184GG (7) = 0x18 + 5 + 1 - 6 + 7 - 7 = 0x18
        PaymentRequest var1 = createPaymentRequest("Store", "X", "SW18 4GG");
        String result1 = converter.convertToBinary(var1);
        assertTrue(result1.contains("18"), "Address length variation should produce 0x18");

        // Variation 2: Different postcode length
        // Store (5) + London (6) + M11AA (5) = 0x18 + 5 + 6 - 6 + 5 - 7 = 0x1B
        PaymentRequest var2 = createPaymentRequest("Store", "London", "M1 1AA");
        String result2 = converter.convertToBinary(var2);
        assertTrue(result2.contains("1B"), "Postcode length variation should produce 0x1B");
    }
}
